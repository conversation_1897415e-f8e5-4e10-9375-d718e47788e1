import sqlite3
import os
from datetime import datetime

class Database:
    def __init__(self, db_name="jewelry_store.db"):
        """Initialize database connection"""
        self.db_name = db_name
        self.conn = None
        self.cursor = None
        self.connect()
        self.create_tables()

    def connect(self):
        """Connect to the SQLite database"""
        try:
            self.conn = sqlite3.connect(self.db_name)
            self.conn.row_factory = sqlite3.Row  # Return rows as dictionaries
            self.cursor = self.conn.cursor()
            print(f"Connected to database: {self.db_name}")
        except sqlite3.Error as e:
            print(f"Database connection error: {e}")

    def close(self):
        """Close the database connection"""
        if self.conn:
            self.conn.close()
            print("Database connection closed")

    def commit(self):
        """Commit changes to the database"""
        if self.conn:
            self.conn.commit()

    def create_tables(self):
        """Create database tables if they don't exist"""
        # Categories table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Products table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE,
            name TEXT NOT NULL,
            category_id INTEGER,
            description TEXT,
            weight REAL,
            purity TEXT,
            purchase_price REAL,
            selling_price REAL,
            image_path TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories (id)
        )
        ''')

        # Inventory table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS inventory (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER,
            quantity INTEGER DEFAULT 0,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        ''')

        # Customers table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            account_type TEXT NOT NULL DEFAULT 'customer',
            entity_type TEXT NOT NULL DEFAULT 'individual',
            main_group_code TEXT,
            sub_group_code TEXT,
            dimension_code TEXT,

            -- Individual fields
            national_id TEXT,
            birth_date TEXT,
            mobile TEXT,
            phone TEXT,
            referrer TEXT,

            -- Legal entity fields
            company_name TEXT,
            economic_code TEXT,
            ceo_name TEXT,
            registration_number TEXT,
            contact_name TEXT,
            contact_national_id TEXT,
            contact_birth_date TEXT,
            contact_mobile TEXT,
            contact_phone TEXT,

            email TEXT,
            address1 TEXT,
            postal_code1 TEXT,
            address2 TEXT,
            postal_code2 TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Sales table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS sales (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_number TEXT UNIQUE,
            customer_id INTEGER,
            sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            total_amount REAL,
            payment_method TEXT,
            notes TEXT,
            user_id INTEGER,
            invoice_path TEXT,
            FOREIGN KEY (customer_id) REFERENCES customers (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')

        # Sale items table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS sale_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sale_id INTEGER,
            product_id INTEGER,
            quantity INTEGER,
            price REAL,
            FOREIGN KEY (sale_id) REFERENCES sales (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        ''')

        # Suppliers table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            contact_person TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Purchases table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS purchases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            supplier_id INTEGER,
            purchase_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            total_amount REAL,
            notes TEXT,
            user_id INTEGER,
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')

        # Purchase items table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS purchase_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            purchase_id INTEGER,
            product_id INTEGER,
            quantity INTEGER,
            price REAL,
            FOREIGN KEY (purchase_id) REFERENCES purchases (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        ''')

        # Users table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            password TEXT NOT NULL,
            full_name TEXT NOT NULL,
            email TEXT,
            phone TEXT,
            role_id INTEGER,
            is_active INTEGER DEFAULT 1,
            last_login TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (role_id) REFERENCES roles (id)
        )
        ''')

        # Roles table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS roles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Permissions table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Role permissions table (many-to-many relationship)
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS role_permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            role_id INTEGER,
            permission_id INTEGER,
            FOREIGN KEY (role_id) REFERENCES roles (id),
            FOREIGN KEY (permission_id) REFERENCES permissions (id),
            UNIQUE(role_id, permission_id)
        )
        ''')

        # User activity log table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action TEXT NOT NULL,
            details TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')

        self.commit()
        print("Database tables created successfully")

        # اضافه کردن نقش‌های پیش‌فرض اگر وجود نداشته باشند
        self.create_default_roles_and_permissions()

    # متدهای مربوط به محصولات
    def add_product(self, code, name, category_id, description, weight, purity, purchase_price, selling_price, image_path=None):
        """افزودن محصول جدید"""
        try:
            self.cursor.execute('''
            INSERT INTO products (code, name, category_id, description, weight, purity, purchase_price, selling_price, image_path)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (code, name, category_id, description, weight, purity, purchase_price, selling_price, image_path))

            product_id = self.cursor.lastrowid

            # افزودن به موجودی با مقدار اولیه 0
            self.cursor.execute('''
            INSERT INTO inventory (product_id, quantity)
            VALUES (?, 0)
            ''', (product_id,))

            self.commit()
            return product_id
        except sqlite3.Error as e:
            print(f"خطا در افزودن محصول: {e}")
            return None

    def update_product(self, product_id, code, name, category_id, description, weight, purity, purchase_price, selling_price, image_path=None):
        """به‌روزرسانی محصول"""
        try:
            self.cursor.execute('''
            UPDATE products
            SET code = ?, name = ?, category_id = ?, description = ?, weight = ?, purity = ?,
                purchase_price = ?, selling_price = ?, image_path = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            ''', (code, name, category_id, description, weight, purity, purchase_price, selling_price, image_path, product_id))

            self.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطا در به‌روزرسانی محصول: {e}")
            return False

    def delete_product(self, product_id):
        """حذف محصول"""
        try:
            # حذف از موجودی
            self.cursor.execute("DELETE FROM inventory WHERE product_id = ?", (product_id,))

            # حذف محصول
            self.cursor.execute("DELETE FROM products WHERE id = ?", (product_id,))

            self.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطا در حذف محصول: {e}")
            return False

    def get_product(self, product_id):
        """دریافت اطلاعات یک محصول"""
        try:
            self.cursor.execute('''
            SELECT p.*, c.name as category_name, i.quantity
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN inventory i ON p.id = i.product_id
            WHERE p.id = ?
            ''', (product_id,))

            return dict(self.cursor.fetchone())
        except sqlite3.Error as e:
            print(f"خطا در دریافت محصول: {e}")
            return None

    def get_all_products(self):
        """دریافت همه محصولات"""
        try:
            self.cursor.execute('''
            SELECT p.*, c.name as category_name, i.quantity
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN inventory i ON p.id = i.product_id
            ORDER BY p.name
            ''')

            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطا در دریافت محصولات: {e}")
            return []

    # متدهای مربوط به دسته‌بندی‌ها
    def add_category(self, name, description=None):
        """افزودن دسته‌بندی جدید"""
        try:
            self.cursor.execute('''
            INSERT INTO categories (name, description)
            VALUES (?, ?)
            ''', (name, description))

            self.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطا در افزودن دسته‌بندی: {e}")
            return None

    def get_all_categories(self):
        """دریافت همه دسته‌بندی‌ها"""
        try:
            self.cursor.execute("SELECT * FROM categories ORDER BY name")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطا در دریافت دسته‌بندی‌ها: {e}")
            return []

    # متدهای مربوط به موجودی
    def update_inventory(self, product_id, quantity):
        """به‌روزرسانی موجودی محصول"""
        try:
            self.cursor.execute('''
            UPDATE inventory
            SET quantity = ?, last_updated = CURRENT_TIMESTAMP
            WHERE product_id = ?
            ''', (quantity, product_id))

            self.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطا در به‌روزرسانی موجودی: {e}")
            return False

    def get_low_stock_products(self, threshold=5):
        """دریافت محصولات با موجودی کم"""
        try:
            self.cursor.execute('''
            SELECT p.*, i.quantity
            FROM products p
            JOIN inventory i ON p.id = i.product_id
            WHERE i.quantity <= ?
            ORDER BY i.quantity
            ''', (threshold,))

            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطا در دریافت محصولات با موجودی کم: {e}")
            return []

    # متدهای مربوط به مشتریان
    def add_customer(self, data):
        """افزودن مشتری جدید"""
        try:
            if data['entity_type'] == 'individual':
                self.cursor.execute('''
                INSERT INTO customers (
                    name, account_type, entity_type, main_group_code, sub_group_code, dimension_code,
                    national_id, birth_date, mobile, phone, referrer,
                    email, address1, postal_code1, address2, postal_code2
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    data['name'], data['account_type'], data['entity_type'],
                    data['main_group_code'], data['sub_group_code'], data['dimension_code'],
                    data['national_id'], data['birth_date'], data['mobile'], data['phone'], data['referrer'],
                    data['email'], data['address1'], data['postal_code1'], data['address2'], data['postal_code2']
                ))
            else:  # Legal entity
                self.cursor.execute('''
                INSERT INTO customers (
                    name, account_type, entity_type, main_group_code, sub_group_code, dimension_code,
                    company_name, economic_code, ceo_name, registration_number,
                    contact_name, contact_national_id, contact_birth_date, contact_mobile, contact_phone,
                    email, address1, postal_code1, address2, postal_code2
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    data['name'], data['account_type'], data['entity_type'],
                    data['main_group_code'], data['sub_group_code'], data['dimension_code'],
                    data['company_name'], data['economic_code'], data['ceo_name'], data['registration_number'],
                    data['contact_name'], data['contact_national_id'], data['contact_birth_date'],
                    data['contact_mobile'], data['contact_phone'],
                    data['email'], data['address1'], data['postal_code1'], data['address2'], data['postal_code2']
                ))

            self.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطا در افزودن مشتری: {e}")
            return None

    def get_all_customers(self):
        """دریافت همه مشتریان"""
        try:
            self.cursor.execute('''
            SELECT id, name, account_type, entity_type, main_group_code, sub_group_code, dimension_code,
                   CASE entity_type
                       WHEN 'individual' THEN national_id
                       ELSE company_name
                   END as display_id,
                   CASE entity_type
                       WHEN 'individual' THEN mobile
                       ELSE contact_mobile
                   END as display_phone,
                   email, created_at
            FROM customers
            ORDER BY name
            ''')
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطا در دریافت مشتریان: {e}")
            return []

    def get_customer(self, customer_id):
        """دریافت اطلاعات یک مشتری"""
        try:
            self.cursor.execute("SELECT * FROM customers WHERE id = ?", (customer_id,))
            customer = self.cursor.fetchone()
            return dict(customer) if customer else None
        except sqlite3.Error as e:
            print(f"خطا در دریافت اطلاعات مشتری: {e}")
            return None

    def update_customer(self, customer_id, data):
        """به‌روزرسانی مشتری"""
        try:
            if data['entity_type'] == 'individual':
                self.cursor.execute('''
                UPDATE customers
                SET name = ?, account_type = ?, entity_type = ?,
                    main_group_code = ?, sub_group_code = ?, dimension_code = ?,
                    national_id = ?, birth_date = ?, mobile = ?, phone = ?, referrer = ?,
                    email = ?, address1 = ?, postal_code1 = ?, address2 = ?, postal_code2 = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                ''', (
                    data['name'], data['account_type'], data['entity_type'],
                    data['main_group_code'], data['sub_group_code'], data['dimension_code'],
                    data['national_id'], data['birth_date'], data['mobile'], data['phone'], data['referrer'],
                    data['email'], data['address1'], data['postal_code1'], data['address2'], data['postal_code2'],
                    customer_id
                ))
            else:  # Legal entity
                self.cursor.execute('''
                UPDATE customers
                SET name = ?, account_type = ?, entity_type = ?,
                    main_group_code = ?, sub_group_code = ?, dimension_code = ?,
                    company_name = ?, economic_code = ?, ceo_name = ?, registration_number = ?,
                    contact_name = ?, contact_national_id = ?, contact_birth_date = ?,
                    contact_mobile = ?, contact_phone = ?,
                    email = ?, address1 = ?, postal_code1 = ?, address2 = ?, postal_code2 = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                ''', (
                    data['name'], data['account_type'], data['entity_type'],
                    data['main_group_code'], data['sub_group_code'], data['dimension_code'],
                    data['company_name'], data['economic_code'], data['ceo_name'], data['registration_number'],
                    data['contact_name'], data['contact_national_id'], data['contact_birth_date'],
                    data['contact_mobile'], data['contact_phone'],
                    data['email'], data['address1'], data['postal_code1'], data['address2'], data['postal_code2'],
                    customer_id
                ))

            self.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطا در به‌روزرسانی مشتری: {e}")
            return False

    def delete_customer(self, customer_id):
        """حذف مشتری"""
        try:
            # بررسی وجود فروش برای این مشتری
            self.cursor.execute("SELECT COUNT(*) FROM sales WHERE customer_id = ?", (customer_id,))
            count = self.cursor.fetchone()[0]

            if count > 0:
                print(f"این مشتری دارای {count} فاکتور فروش است و نمی‌توان آن را حذف کرد")
                return False

            # حذف مشتری
            self.cursor.execute("DELETE FROM customers WHERE id = ?", (customer_id,))

            self.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطا در حذف مشتری: {e}")
            return False

    # متدهای مربوط به فروش
    def add_sale(self, invoice_number, customer_id, total_amount, payment_method, notes=None):
        """افزودن فروش جدید"""
        try:
            self.cursor.execute('''
            INSERT INTO sales (invoice_number, customer_id, total_amount, payment_method, notes)
            VALUES (?, ?, ?, ?, ?)
            ''', (invoice_number, customer_id, total_amount, payment_method, notes))

            sale_id = self.cursor.lastrowid
            self.commit()
            return sale_id
        except sqlite3.Error as e:
            print(f"خطا در افزودن فروش: {e}")
            return None

    def add_sale_item(self, sale_id, product_id, quantity, price):
        """افزودن آیتم به فروش"""
        try:
            self.cursor.execute('''
            INSERT INTO sale_items (sale_id, product_id, quantity, price)
            VALUES (?, ?, ?, ?)
            ''', (sale_id, product_id, quantity, price))

            # به‌روزرسانی موجودی
            self.cursor.execute('''
            UPDATE inventory
            SET quantity = quantity - ?, last_updated = CURRENT_TIMESTAMP
            WHERE product_id = ?
            ''', (quantity, product_id))

            self.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطا در افزودن آیتم فروش: {e}")
            return False

    def get_recent_sales(self, limit=10):
        """دریافت فروش‌های اخیر"""
        try:
            self.cursor.execute('''
            SELECT s.*, c.name as customer_name
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            ORDER BY s.sale_date DESC
            LIMIT ?
            ''', (limit,))

            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطا در دریافت فروش‌های اخیر: {e}")
            return []

    def get_sales_report(self, start_date=None, customer_id=None):
        """دریافت گزارش فروش با فیلتر"""
        try:
            query = '''
            SELECT s.*, c.name as customer_name
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            WHERE 1=1
            '''
            params = []

            if start_date:
                query += " AND DATE(s.sale_date) >= DATE(?)"
                params.append(str(start_date))

            if customer_id:
                query += " AND s.customer_id = ?"
                params.append(customer_id)

            query += " ORDER BY s.sale_date DESC"

            self.cursor.execute(query, params)

            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطا در دریافت گزارش فروش: {e}")
            return []

    def get_products_report(self):
        """دریافت گزارش محصولات"""
        try:
            self.cursor.execute('''
            SELECT p.id, p.code, p.name, c.name as category_name,
                   COUNT(si.id) as sales_count,
                   SUM(si.price * si.quantity) as sales_amount
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN sale_items si ON p.id = si.product_id
            GROUP BY p.id
            ORDER BY sales_count DESC
            ''')

            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطا در دریافت گزارش محصولات: {e}")
            return []

    def get_customers_report(self):
        """دریافت گزارش مشتریان"""
        try:
            self.cursor.execute('''
            SELECT c.id, c.name, c.phone, c.email,
                   COUNT(s.id) as sales_count,
                   SUM(s.total_amount) as sales_amount
            FROM customers c
            LEFT JOIN sales s ON c.id = s.customer_id
            GROUP BY c.id
            ORDER BY sales_amount DESC
            ''')

            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطا در دریافت گزارش مشتریان: {e}")
            return []

    def get_sale_details(self, sale_id):
        """دریافت جزئیات یک فروش"""
        try:
            # دریافت اطلاعات فروش
            self.cursor.execute('''
            SELECT s.*, c.name as customer_name, c.phone as customer_phone
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            WHERE s.id = ?
            ''', (sale_id,))

            sale = dict(self.cursor.fetchone())

            # دریافت آیتم‌های فروش
            self.cursor.execute('''
            SELECT si.*, p.name as product_name, p.code as product_code
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            WHERE si.sale_id = ?
            ''', (sale_id,))

            sale_items = [dict(row) for row in self.cursor.fetchall()]

            return {
                "sale": sale,
                "items": sale_items
            }
        except sqlite3.Error as e:
            print(f"خطا در دریافت جزئیات فروش: {e}")
            return None

    def update_sale_invoice_path(self, sale_id, invoice_path):
        """به‌روزرسانی مسیر فاکتور یک فروش"""
        try:
            self.cursor.execute(
                "UPDATE sales SET invoice_path = ? WHERE id = ?",
                (invoice_path, sale_id)
            )
            self.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطا در به‌روزرسانی مسیر فاکتور: {e}")
            return False

    def get_sale_invoice_path(self, sale_id):
        """دریافت مسیر فاکتور یک فروش"""
        try:
            self.cursor.execute(
                "SELECT invoice_path FROM sales WHERE id = ?",
                (sale_id,)
            )
            result = self.cursor.fetchone()
            if result and result["invoice_path"]:
                return result["invoice_path"]
            return None
        except sqlite3.Error as e:
            print(f"خطا در دریافت مسیر فاکتور: {e}")
            return None

    def create_default_roles_and_permissions(self):
        """ایجاد نقش‌ها و مجوزهای پیش‌فرض"""
        try:
            # بررسی وجود نقش‌ها
            self.cursor.execute("SELECT COUNT(*) FROM roles")
            roles_count = self.cursor.fetchone()[0]

            if roles_count == 0:
                # تعریف نقش‌های پیش‌فرض
                default_roles = [
                    ("admin", "مدیر سیستم با دسترسی کامل"),
                    ("manager", "مدیر فروشگاه با دسترسی به همه بخش‌ها به جز تنظیمات سیستم"),
                    ("sales", "فروشنده با دسترسی به فروش و مشتریان"),
                    ("inventory", "انباردار با دسترسی به موجودی و محصولات"),
                    ("accountant", "حسابدار با دسترسی به گزارشات و فروش‌ها")
                ]

                for role_name, role_description in default_roles:
                    self.cursor.execute(
                        "INSERT INTO roles (name, description) VALUES (?, ?)",
                        (role_name, role_description)
                    )

                self.commit()
                print("نقش‌های پیش‌فرض با موفقیت ایجاد شدند")

            # بررسی وجود مجوزها
            self.cursor.execute("SELECT COUNT(*) FROM permissions")
            permissions_count = self.cursor.fetchone()[0]

            if permissions_count == 0:
                # تعریف مجوزهای پیش‌فرض
                default_permissions = [
                    # مجوزهای مربوط به محصولات
                    ("view_products", "مشاهده محصولات"),
                    ("add_product", "افزودن محصول جدید"),
                    ("edit_product", "ویرایش محصول"),
                    ("delete_product", "حذف محصول"),

                    # مجوزهای مربوط به مشتریان
                    ("view_customers", "مشاهده مشتریان"),
                    ("add_customer", "افزودن مشتری جدید"),
                    ("edit_customer", "ویرایش مشتری"),
                    ("delete_customer", "حذف مشتری"),

                    # مجوزهای مربوط به فروش
                    ("view_sales", "مشاهده فروش‌ها"),
                    ("add_sale", "ثبت فروش جدید"),
                    ("view_sale_details", "مشاهده جزئیات فروش"),

                    # مجوزهای مربوط به موجودی
                    ("view_inventory", "مشاهده موجودی"),
                    ("update_inventory", "به‌روزرسانی موجودی"),

                    # مجوزهای مربوط به گزارشات
                    ("view_reports", "مشاهده گزارشات"),
                    ("export_reports", "خروجی گرفتن از گزارشات"),

                    # مجوزهای مربوط به تامین‌کنندگان و خرید
                    ("view_suppliers", "مشاهده تامین‌کنندگان"),
                    ("add_supplier", "افزودن تامین‌کننده جدید"),
                    ("edit_supplier", "ویرایش تامین‌کننده"),
                    ("delete_supplier", "حذف تامین‌کننده"),
                    ("view_purchases", "مشاهده خریدها"),
                    ("add_purchase", "ثبت خرید جدید"),

                    # مجوزهای مربوط به پشتیبان‌گیری
                    ("backup_database", "تهیه نسخه پشتیبان"),
                    ("restore_database", "بازیابی اطلاعات"),

                    # مجوزهای مربوط به کاربران و مجوزها
                    ("view_users", "مشاهده کاربران"),
                    ("add_user", "افزودن کاربر جدید"),
                    ("edit_user", "ویرایش کاربر"),
                    ("delete_user", "حذف کاربر"),
                    ("view_roles", "مشاهده نقش‌ها"),
                    ("manage_roles", "مدیریت نقش‌ها و مجوزها"),

                    # مجوزهای مربوط به تنظیمات سیستم
                    ("view_settings", "مشاهده تنظیمات"),
                    ("edit_settings", "ویرایش تنظیمات")
                ]

                for permission_name, permission_description in default_permissions:
                    self.cursor.execute(
                        "INSERT INTO permissions (name, description) VALUES (?, ?)",
                        (permission_name, permission_description)
                    )

                self.commit()
                print("مجوزهای پیش‌فرض با موفقیت ایجاد شدند")

                # تخصیص مجوزها به نقش‌ها
                # دریافت شناسه نقش‌ها
                self.cursor.execute("SELECT id, name FROM roles")
                roles = {row[1]: row[0] for row in self.cursor.fetchall()}

                # دریافت شناسه مجوزها
                self.cursor.execute("SELECT id, name FROM permissions")
                permissions = {row[1]: row[0] for row in self.cursor.fetchall()}

                # تعریف مجوزهای هر نقش
                role_permissions = {
                    "admin": [perm for perm in permissions.keys()],  # همه مجوزها

                    "manager": [
                        "view_products", "add_product", "edit_product", "delete_product",
                        "view_customers", "add_customer", "edit_customer", "delete_customer",
                        "view_sales", "add_sale", "view_sale_details",
                        "view_inventory", "update_inventory",
                        "view_reports", "export_reports",
                        "view_suppliers", "add_supplier", "edit_supplier", "delete_supplier",
                        "view_purchases", "add_purchase",
                        "backup_database", "restore_database",
                        "view_users", "add_user", "edit_user"
                    ],

                    "sales": [
                        "view_products",
                        "view_customers", "add_customer", "edit_customer",
                        "view_sales", "add_sale", "view_sale_details",
                        "view_inventory"
                    ],

                    "inventory": [
                        "view_products", "add_product", "edit_product",
                        "view_inventory", "update_inventory",
                        "view_suppliers", "view_purchases"
                    ],

                    "accountant": [
                        "view_products",
                        "view_customers",
                        "view_sales", "view_sale_details",
                        "view_inventory",
                        "view_reports", "export_reports",
                        "view_purchases"
                    ]
                }

                # تخصیص مجوزها به نقش‌ها
                for role_name, permission_list in role_permissions.items():
                    role_id = roles.get(role_name)
                    if role_id:
                        for permission_name in permission_list:
                            permission_id = permissions.get(permission_name)
                            if permission_id:
                                try:
                                    self.cursor.execute(
                                        "INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)",
                                        (role_id, permission_id)
                                    )
                                except sqlite3.IntegrityError:
                                    # مجوز قبلاً به این نقش اختصاص داده شده است
                                    pass

                self.commit()
                print("مجوزها با موفقیت به نقش‌ها تخصیص داده شدند")

                # ایجاد کاربر مدیر پیش‌فرض اگر هیچ کاربری وجود نداشته باشد
                self.cursor.execute("SELECT COUNT(*) FROM users")
                users_count = self.cursor.fetchone()[0]

                if users_count == 0:
                    admin_role_id = roles.get("admin")
                    if admin_role_id:
                        # رمز عبور پیش‌فرض: admin123
                        import hashlib
                        password = hashlib.sha256("admin123".encode()).hexdigest()

                        self.cursor.execute(
                            "INSERT INTO users (username, password, full_name, role_id) VALUES (?, ?, ?, ?)",
                            ("admin", password, "مدیر سیستم", admin_role_id)
                        )

                        self.commit()
                        print("کاربر مدیر پیش‌فرض با موفقیت ایجاد شد")

        except sqlite3.Error as e:
            print(f"خطا در ایجاد نقش‌ها و مجوزهای پیش‌فرض: {e}")

    def backup_database(self, backup_path=None):
        """پشتیبان‌گیری از پایگاه داده"""
        import shutil
        from datetime import datetime
        import os

        try:
            # بستن اتصال فعلی به پایگاه داده
            self.close()

            # ایجاد نام فایل پشتیبان با تاریخ و زمان فعلی
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_filename = f"backup_{timestamp}_{os.path.basename(self.db_name)}"

                # ایجاد پوشه پشتیبان اگر وجود ندارد
                backup_dir = "backups"
                if not os.path.exists(backup_dir):
                    os.makedirs(backup_dir)

                backup_path = os.path.join(backup_dir, backup_filename)

            # کپی فایل پایگاه داده به عنوان پشتیبان
            shutil.copy2(self.db_name, backup_path)

            # اتصال مجدد به پایگاه داده
            self.connect()

            print(f"پشتیبان‌گیری با موفقیت در {backup_path} ذخیره شد")
            return backup_path

        except Exception as e:
            print(f"خطا در پشتیبان‌گیری از پایگاه داده: {e}")
            # اتصال مجدد به پایگاه داده در صورت خطا
            self.connect()
            return None

    def restore_database(self, backup_path):
        """بازیابی پایگاه داده از فایل پشتیبان"""
        import shutil
        import os

        try:
            # بررسی وجود فایل پشتیبان
            if not os.path.exists(backup_path):
                print(f"فایل پشتیبان {backup_path} یافت نشد")
                return False

            # بستن اتصال فعلی به پایگاه داده
            self.close()

            # کپی فایل پشتیبان به عنوان پایگاه داده اصلی
            shutil.copy2(backup_path, self.db_name)

            # اتصال مجدد به پایگاه داده
            self.connect()

            print(f"پایگاه داده با موفقیت از {backup_path} بازیابی شد")
            return True

        except Exception as e:
            print(f"خطا در بازیابی پایگاه داده: {e}")
            # اتصال مجدد به پایگاه داده در صورت خطا
            self.connect()
            return False

    # متدهای مربوط به کاربران و مجوزها
    def authenticate_user(self, username, password):
        """احراز هویت کاربر"""
        try:
            import hashlib
            # رمزنگاری رمز عبور
            hashed_password = hashlib.sha256(password.encode()).hexdigest()

            # بررسی نام کاربری و رمز عبور
            self.cursor.execute('''
            SELECT u.*, r.name as role_name
            FROM users u
            LEFT JOIN roles r ON u.role_id = r.id
            WHERE u.username = ? AND u.password = ? AND u.is_active = 1
            ''', (username, hashed_password))

            user = self.cursor.fetchone()

            if user:
                # به‌روزرسانی زمان آخرین ورود
                self.cursor.execute(
                    "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?",
                    (user["id"],)
                )
                self.commit()

                # ثبت فعالیت ورود
                self.log_activity(user["id"], "login", "ورود به سیستم")

                return dict(user)
            else:
                return None

        except sqlite3.Error as e:
            print(f"خطا در احراز هویت کاربر: {e}")
            return None

    def get_user_permissions(self, user_id):
        """دریافت مجوزهای کاربر"""
        try:
            self.cursor.execute('''
            SELECT p.name
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            JOIN users u ON rp.role_id = u.role_id
            WHERE u.id = ?
            ''', (user_id,))

            permissions = [row[0] for row in self.cursor.fetchall()]
            return permissions

        except sqlite3.Error as e:
            print(f"خطا در دریافت مجوزهای کاربر: {e}")
            return []

    def check_permission(self, user_id, permission_name):
        """بررسی دسترسی کاربر به یک مجوز خاص"""
        try:
            self.cursor.execute('''
            SELECT COUNT(*)
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            JOIN users u ON rp.role_id = u.role_id
            WHERE u.id = ? AND p.name = ?
            ''', (user_id, permission_name))

            count = self.cursor.fetchone()[0]
            return count > 0

        except sqlite3.Error as e:
            print(f"خطا در بررسی دسترسی کاربر: {e}")
            return False

    def get_all_users(self):
        """دریافت همه کاربران"""
        try:
            self.cursor.execute('''
            SELECT u.*, r.name as role_name
            FROM users u
            LEFT JOIN roles r ON u.role_id = r.id
            ORDER BY u.username
            ''')

            return [dict(row) for row in self.cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطا در دریافت کاربران: {e}")
            return []

    def get_user(self, user_id):
        """دریافت اطلاعات یک کاربر"""
        try:
            self.cursor.execute('''
            SELECT u.*, r.name as role_name
            FROM users u
            LEFT JOIN roles r ON u.role_id = r.id
            WHERE u.id = ?
            ''', (user_id,))

            user = self.cursor.fetchone()
            return dict(user) if user else None

        except sqlite3.Error as e:
            print(f"خطا در دریافت اطلاعات کاربر: {e}")
            return None

    def add_user(self, username, password, full_name, role_id, email=None, phone=None):
        """افزودن کاربر جدید"""
        try:
            import hashlib
            # رمزنگاری رمز عبور
            hashed_password = hashlib.sha256(password.encode()).hexdigest()

            self.cursor.execute('''
            INSERT INTO users (username, password, full_name, role_id, email, phone)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (username, hashed_password, full_name, role_id, email, phone))

            self.commit()
            return self.cursor.lastrowid

        except sqlite3.Error as e:
            print(f"خطا در افزودن کاربر: {e}")
            return None

    def update_user(self, user_id, full_name, role_id, email=None, phone=None, is_active=1):
        """به‌روزرسانی اطلاعات کاربر"""
        try:
            self.cursor.execute('''
            UPDATE users
            SET full_name = ?, role_id = ?, email = ?, phone = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            ''', (full_name, role_id, email, phone, is_active, user_id))

            self.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطا در به‌روزرسانی کاربر: {e}")
            return False

    def change_password(self, user_id, new_password):
        """تغییر رمز عبور کاربر"""
        try:
            import hashlib
            # رمزنگاری رمز عبور جدید
            hashed_password = hashlib.sha256(new_password.encode()).hexdigest()

            self.cursor.execute(
                "UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (hashed_password, user_id)
            )

            self.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطا در تغییر رمز عبور: {e}")
            return False

    def delete_user(self, user_id):
        """حذف کاربر"""
        try:
            # بررسی وجود فروش یا خرید برای این کاربر
            self.cursor.execute("SELECT COUNT(*) FROM sales WHERE user_id = ?", (user_id,))
            sales_count = self.cursor.fetchone()[0]

            self.cursor.execute("SELECT COUNT(*) FROM purchases WHERE user_id = ?", (user_id,))
            purchases_count = self.cursor.fetchone()[0]

            if sales_count > 0 or purchases_count > 0:
                print(f"این کاربر دارای {sales_count} فروش و {purchases_count} خرید است و نمی‌توان آن را حذف کرد")
                return False

            # حذف کاربر
            self.cursor.execute("DELETE FROM users WHERE id = ?", (user_id,))

            self.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطا در حذف کاربر: {e}")
            return False

    def get_all_roles(self):
        """دریافت همه نقش‌ها"""
        try:
            self.cursor.execute("SELECT * FROM roles ORDER BY name")
            return [dict(row) for row in self.cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطا در دریافت نقش‌ها: {e}")
            return []

    def get_all_permissions(self):
        """دریافت همه مجوزها"""
        try:
            self.cursor.execute("SELECT * FROM permissions ORDER BY name")
            return [dict(row) for row in self.cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطا در دریافت مجوزها: {e}")
            return []

    def get_role_permissions(self, role_id):
        """دریافت مجوزهای یک نقش"""
        try:
            self.cursor.execute('''
            SELECT p.*
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            WHERE rp.role_id = ?
            ORDER BY p.name
            ''', (role_id,))

            return [dict(row) for row in self.cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطا در دریافت مجوزهای نقش: {e}")
            return []

    def update_role_permissions(self, role_id, permission_ids):
        """به‌روزرسانی مجوزهای یک نقش"""
        try:
            # حذف همه مجوزهای فعلی نقش
            self.cursor.execute("DELETE FROM role_permissions WHERE role_id = ?", (role_id,))

            # افزودن مجوزهای جدید
            for permission_id in permission_ids:
                self.cursor.execute(
                    "INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)",
                    (role_id, permission_id)
                )

            self.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطا در به‌روزرسانی مجوزهای نقش: {e}")
            return False

    def log_activity(self, user_id, action, details=None):
        """ثبت فعالیت کاربر"""
        try:
            self.cursor.execute(
                "INSERT INTO activity_logs (user_id, action, details) VALUES (?, ?, ?)",
                (user_id, action, details)
            )

            self.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطا در ثبت فعالیت: {e}")
            return False

    def get_user_activities(self, user_id=None, limit=100):
        """دریافت فعالیت‌های کاربر"""
        try:
            if user_id:
                self.cursor.execute('''
                SELECT al.*, u.username, u.full_name
                FROM activity_logs al
                JOIN users u ON al.user_id = u.id
                WHERE al.user_id = ?
                ORDER BY al.timestamp DESC
                LIMIT ?
                ''', (user_id, limit))
            else:
                self.cursor.execute('''
                SELECT al.*, u.username, u.full_name
                FROM activity_logs al
                JOIN users u ON al.user_id = u.id
                ORDER BY al.timestamp DESC
                LIMIT ?
                ''', (limit,))

            return [dict(row) for row in self.cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطا در دریافت فعالیت‌ها: {e}")
            return []
